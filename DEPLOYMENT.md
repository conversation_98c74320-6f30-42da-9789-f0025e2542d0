# 🚀 D&L Garden Center - Deployment Guide

This guide will help you deploy the D&L Garden Center website to a production server.

## 📋 Prerequisites

- **Web Server:** Apache or Nginx
- **PHP:** 8.2 or higher
- **Database:** MariaDB 10.6+ or MySQL 8.0+
- **SSL Certificate:** Recommended for production

## 🛠️ Quick Deployment Steps

### 1. Clone Repository

```bash
git clone https://github.com/joanncodex/dlgardencenter.git
cd dlgardencenter
```

### 2. Configure Web Server

**Apache (.htaccess included):**
- Point document root to the project directory
- Ensure mod_rewrite is enabled
- The included .htaccess handles URL rewriting

**Nginx:**
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/dlgardencenter;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 3. Database Setup

**Create Database:**
```sql
CREATE DATABASE dlgarden_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'dlgarden_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON dlgarden_db.* TO 'dlgarden_user'@'localhost';
FLUSH PRIVILEGES;
```

**Install Schema:**
Visit: `https://yourdomain.com/sql/simple-install.php?install=confirm`

### 4. Configuration

**Update Database Settings:**
Edit `includes/config.php`:
```php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'dlgarden_db');
define('DB_USER', 'dlgarden_user');
define('DB_PASS', 'your_secure_password');

// Set to false for production
define('DEVELOPMENT_MODE', false);
```

### 5. Security Hardening

**File Permissions:**
```bash
chmod 644 includes/config.php
chmod 755 sql/
chmod 644 *.php
```

**Remove Development Files:**
```bash
rm test-db-connection.php
rm setup-database.sh
rm sql/simple-install.php  # After installation
```

**Update .htaccess for Production:**
Add security headers and disable directory browsing.

### 6. SSL Certificate

**Let's Encrypt (Recommended):**
```bash
sudo certbot --apache -d yourdomain.com
```

## 🔧 Environment-Specific Configuration

### Production Checklist

- [ ] Database credentials updated
- [ ] `DEVELOPMENT_MODE` set to `false`
- [ ] SSL certificate installed
- [ ] Development files removed
- [ ] File permissions secured
- [ ] Error reporting disabled
- [ ] Backup system configured

### Staging Environment

- [ ] Separate database for staging
- [ ] `DEVELOPMENT_MODE` can remain `true`
- [ ] Test all functionality
- [ ] Verify responsive design
- [ ] Test contact form submissions

## 📊 Performance Optimization

### Recommended Optimizations

1. **Enable Gzip Compression**
2. **Set Browser Caching Headers**
3. **Optimize Images**
4. **Enable PHP OPcache**
5. **Use CDN for Bootstrap/jQuery**

### Apache Optimizations (.htaccess)

The included .htaccess already contains:
- Gzip compression
- Browser caching
- Security headers
- URL rewriting

## 🔍 Monitoring & Maintenance

### Health Checks

- Monitor database connections
- Check error logs regularly
- Verify contact form functionality
- Test slideshow performance

### Backup Strategy

**Database Backup:**
```bash
mysqldump -u dlgarden_user -p dlgarden_db > backup_$(date +%Y%m%d).sql
```

**File Backup:**
```bash
tar -czf website_backup_$(date +%Y%m%d).tar.gz /path/to/dlgardencenter
```

## 🆘 Troubleshooting

### Common Issues

**Database Connection Errors:**
- Check credentials in `includes/config.php`
- Verify database server is running
- Check user permissions

**URL Rewriting Issues:**
- Ensure mod_rewrite is enabled (Apache)
- Check .htaccess permissions
- Verify server configuration

**Contact Form Not Working:**
- Check database connection
- Verify table exists: `contact_submissions`
- Check PHP error logs

### Support

For technical support or questions:
- Check the README.md file
- Review error logs
- Test with `test-db-connection.php` (development only)

## 📱 Mobile Testing

Ensure to test on:
- Various mobile devices
- Different screen sizes
- Touch interactions
- Slideshow functionality
- Contact form usability

---

**Note:** Always test thoroughly in a staging environment before deploying to production.
