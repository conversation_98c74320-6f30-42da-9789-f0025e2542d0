/* D&L Garden Center - Modern CSS Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #198754;
    --primary-dark: #146c43;
    --secondary-color: #6c757d;
    --accent-color: #ffc107;
    --text-dark: #212529;
    --text-light: #6c757d;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --border-color: #dee2e6;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 0.375rem;
    --transition: all 0.3s ease;
    
    /* Typography */
    --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-heading: 'Playfair Display', Georgia, serif;
    --font-size-base: 1rem;
    --line-height-base: 1.6;
}

/* Base Styles */
body {
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--text-dark);
    background-color: var(--bg-white);
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.1rem; }

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* Header Styles */
.site-header {
    position: sticky;
    top: 0;
    z-index: 1020;
}

.top-bar {
    font-size: 0.875rem;
}

.logo-img {
    max-height: 60px;
    width: auto;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
    color: var(--text-dark);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color);
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-md);
    border-radius: var(--border-radius);
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
}

/* Hero Slideshow - Industry Standard Height */
.hero-slideshow {
    position: relative;
    overflow: hidden;
    height: 80vh;
    min-height: 700px;
    max-height: 900px;
}

.carousel,
.carousel-inner,
.carousel-item {
    height: 100%;
}

.carousel-item {
    overflow: hidden;
}

/* Ensure all slides are exactly the same height */
.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
    height: 100%;
}

.hero-slide {
    height: 80vh;
    min-height: 700px;
    max-height: 900px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    position: relative;
}

.min-vh-75 {
    height: 100%;
    min-height: 100%;
    display: flex;
    align-items: center;
}

.hero-content {
    z-index: 2;
    position: relative;
    width: 100%;
    padding: 5rem 3rem;
}

.hero-title {
    font-family: var(--font-family-heading);
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    line-height: 1.4;
}

/* Carousel Customization */
.carousel-indicators {
    bottom: 30px;
    z-index: 3;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    background-color: transparent;
    opacity: 0.7;
    transition: var(--transition);
}

.carousel-indicators button.active {
    background-color: white;
    opacity: 1;
    transform: scale(1.2);
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.8;
    transition: var(--transition);
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 3rem;
    height: 3rem;
    background-size: 100%;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.5));
}

/* Slide Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 1s ease-out forwards;
}

.slide-in-right {
    animation: slideInRight 1s ease-out forwards;
}

.slide-in-up {
    animation: slideInUp 1s ease-out forwards;
}

.slide-in-down {
    animation: slideInDown 1s ease-out forwards;
}

/* Hero Features */
.hero-features ul {
    font-size: 1.1rem;
}

.delivery-features .feature-item {
    font-size: 1.1rem;
    font-weight: 500;
}

.badge-item {
    background: rgba(255,255,255,0.1);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.dealer-badges img {
    filter: brightness(0) invert(1);
}

/* Parallax Effect */
.hero-slide {
    background-attachment: fixed;
}

/* Disable parallax on mobile for better performance */
@media (max-width: 768px) {
    .hero-slide {
        background-attachment: scroll;
    }
}

/* Slide Transition Effects */
.carousel-item {
    transition: transform 0.8s ease-in-out;
}

.carousel-item.active {
    transform: scale(1);
}

.carousel-item-next,
.carousel-item-prev {
    transform: scale(1.05);
}

/* Loading Animation for Hero */
.hero-slideshow {
    opacity: 0;
    animation: fadeInHero 1s ease-in-out 0.5s forwards;
}

.hero-loading {
    height: 100% !important;
}

@keyframes fadeInHero {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Enhanced Button Styles for Hero */
.hero-buttons .btn {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-width: 2px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.hero-buttons .btn-success:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* Improved Carousel Indicators */
.carousel-indicators {
    gap: 0.5rem;
}

.carousel-indicators button {
    transition: all 0.3s ease;
}

.carousel-indicators button:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
}

/* Pricing Tables */
.pricing-table {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.pricing-table .table {
    margin-bottom: 0;
}

.pricing-table .table th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.pricing-table .table td {
    padding: 0.75rem 1rem;
    border-color: var(--border-color);
}

.pricing-table .table tbody tr:nth-child(even) {
    background-color: var(--bg-light);
}

/* Gallery Styles */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.gallery-item:hover {
    box-shadow: var(--shadow-md);
    transform: scale(1.02);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

/* Footer Styles */
.site-footer {
    margin-top: auto;
}

.footer-title {
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a:hover {
    color: white !important;
}

.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-item i {
    margin-top: 0.25rem;
    color: var(--accent-color);
}

/* Back to Top Button */
#btn-back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: none;
    z-index: 1000;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-slideshow {
        height: 70vh;
        min-height: 600px;
        max-height: 750px;
    }

    .hero-content {
        padding: 4rem 2rem;
    }

    .hero-title {
        font-size: 2.5rem !important;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
        margin-right: 0 !important;
    }

    .hero-features,
    .delivery-features {
        font-size: 1rem;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 8%;
    }

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 2rem;
        height: 2rem;
    }

    .carousel-indicators {
        bottom: 20px;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .pricing-table {
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .hero-slideshow {
        height: 60vh;
        min-height: 550px;
        max-height: 650px;
    }

    .hero-content {
        padding: 3rem 1.5rem;
    }

    .hero-title {
        font-size: 2rem !important;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .gallery-grid {
        grid-template-columns: 1fr 1fr;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .hero-slideshow {
        height: 55vh;
        min-height: 500px;
        max-height: 600px;
    }

    .hero-content {
        padding: 2.5rem 1rem;
    }

    .hero-title {
        font-size: 1.75rem !important;
    }

    .hero-subtitle {
        font-size: 0.95rem;
    }
}

/* Utility Classes */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.border-primary-custom {
    border-color: var(--primary-color) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}
